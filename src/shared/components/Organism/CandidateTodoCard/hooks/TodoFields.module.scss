@import '/src/shared/theme/theme.scss';

@layer organism {
  .datepicker {
    width: auto;
  }
  .labelstyle,
  .placeholder {
    &::placeholder {
      color: colors(colorIconForth2);
    }
  }
  .qlEditor {
    padding: 0 !important;
    border-radius: 0 !important;

    :global {
      & .ql-editor {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        &::before {
          color: colors(colorIconForth2) !important;
        }
      }
    }
  }

  // Note: Click-outside exception handling is now managed automatically
  // by the enhanced PopperMenu component logic, so no additional CSS rules are needed
}

@import '/src/shared/theme/theme.scss';

@layer organism {
  .datepicker {
    width: auto;
  }
  .labelstyle,
  .placeholder {
    &::placeholder {
      color: colors(colorIconForth2);
    }
  }
  .qlEditor {
    padding: 0 !important;
    border-radius: 0 !important;

    :global {
      & .ql-editor {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        &::before {
          color: colors(colorIconForth2) !important;
        }
      }
    }
  }

  // Global exception classes for click-outside behavior
  :global {
    // Date picker exceptions - applies to all date picker related elements
    .todo-dates-exceptions,
    .todo-dates-exceptions *,
    .react-datepicker,
    .react-datepicker *,
    .react-datepicker-popper,
    .react-datepicker-popper *,
    [class*="react-datepicker"],
    [class*="react-datepicker"] *,
    [id="autocomplete-options"],
    [id="autocomplete-options"] *,
    [id="LOBOX_DATE_PICKER_BOTTOM_SHEET"],
    [id="LOBOX_DATE_PICKER_BOTTOM_SHEET"] *,
    [id="auto-complete-wrapper-id"],
    [id="auto-complete-wrapper-id"] *,
    [class*="AutoComplete_options"],
    [class*="AutoComplete_options"] *,
    [class*="AutoComplete_root"],
    [class*="AutoComplete_root"] *,
    [class*="AutoComplete_show"],
    [class*="AutoComplete_show"] *,
    [class*="options"],
    [class*="options"] *,
    [class*="dropdown"],
    [class*="dropdown"] *,
    [data-portal="true"],
    [data-portal="true"] *,
    [role="listbox"],
    [role="listbox"] *,
    [role="option"],
    [role="option"] *,
    .POPPER_CLASS,
    .POPPER_CLASS *,
    .uikit3,
    .uikit3 * {
      // This class will be detected by the PopperMenu click-outside logic
    }

    // Assignee picker exceptions - applies to all autocomplete related elements
    .todo-assignee-exceptions,
    .todo-assignee-exceptions *,
    [class*="autocomplete"],
    [class*="autocomplete"] *,
    [id="autocomplete-options"],
    [id="autocomplete-options"] *,
    [id="auto-complete-wrapper-id"],
    [id="auto-complete-wrapper-id"] *,
    [class*="AutoComplete_options"],
    [class*="AutoComplete_options"] *,
    [class*="AutoComplete_root"],
    [class*="AutoComplete_root"] *,
    [class*="AutoComplete_show"],
    [class*="AutoComplete_show"] *,
    [class*="options"],
    [class*="options"] *,
    [class*="dropdown"],
    [class*="dropdown"] *,
    [data-portal="true"],
    [data-portal="true"] *,
    [role="listbox"],
    [role="listbox"] *,
    [role="option"],
    [role="option"] *,
    .POPPER_CLASS,
    .POPPER_CLASS *,
    .uikit3,
    .uikit3 * {
      // This class will be detected by the PopperMenu click-outside logic
    }
  }
}
